"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/providers/toast-provider";
import { Sidebar, PanelContent } from "./components";
import { PreviewArea } from "./components/preview";
import { ReportEditorProps, ReportInfo, Section, Template, StyleOptions } from "./types";

export function ReportEditor({ reportId, templateId = 'professional', styleId, onSave, onCancel }: ReportEditorProps) {
  const router = useRouter();
  const { toast } = useToast();

  // UI State
  const [activeItem, setActiveItem] = useState<string | null>(null);
  const [activeSectionId, setActiveSectionId] = useState<string | null>(null);
  const [activeSectionTitle, setActiveSectionTitle] = useState<string>("");
  const [pulsingSectionId, setPulsingSectionId] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Report Data State
  const [reportInfo, setReportInfo] = useState<ReportInfo>({
    title: "Untitled Report",
    description: "",
    client: undefined,
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [sections, setSections] = useState<Section[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>(templateId);
  const [templateName, setTemplateName] = useState<string>("Default Template");
  const [clients, setClients] = useState<any[]>([]);
  // Initialize styles with minimal defaults to allow AI maximum creativity
  const [styles, setStyles] = useState<StyleOptions>({
    styleTemplateId: styleId || undefined
  });

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch templates
        try {
          const templateResponse = await fetch('/api/templates');
          if (templateResponse.ok) {
            const templatesData = await templateResponse.json(); // Renamed to templatesData
            const actualTemplatesArray = Array.isArray(templatesData) ? templatesData : templatesData.items || []; // Get the actual array
            setTemplates(actualTemplatesArray);

            // Find the selected template
            const selectedTemplateData = actualTemplatesArray.find((t: any) => t.id === templateId); // Use actualTemplatesArray
            if (selectedTemplateData) {
              setTemplateName(selectedTemplateData.name);

              // Initialize with template sections if available
              if (selectedTemplateData.sections && selectedTemplateData.sections.length > 0 && selectedTemplateData.fields && selectedTemplateData.fields.length > 0 && !reportId) {
                // Create sections from the template sections
                const templateSections = selectedTemplateData.sections.map((section: any) => ({
                  id: `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                  title: section.title || "Untitled Section",
                  type: section.type || "title-page",
                  content: section.content || {}
                }));

                // Add fields to the content section
                const contentSection = templateSections.find((s: { title: string }) => s.title === 'Content');
                if (contentSection) {
                  selectedTemplateData.fields.forEach((field: any) => {
                    // Add each field as a property in the content object
                    (contentSection.content as Record<string, string>)[field.id] = field.content || '';
                  });
                }

                setSections(templateSections);
                if (templateSections.length > 0) {
                  setActiveSectionId(templateSections[0].id);
                  setActiveSectionTitle(templateSections[0].title);
                }
              } else if (selectedTemplateData.fields && selectedTemplateData.fields.length > 0 && !reportId) {
              // Handle example template with fields
              const templateSections = [
                {
                  id: `section-${Date.now()}-1`,
                  title: 'Title Page',
                  type: 'title-page',
                  content: {
                    title: selectedTemplateData.name, // Use selectedTemplateData
                    subtitle: "Created with CS-Hero",
                    footer: "Confidential"
                  }
                },
                {
                  id: `section-${Date.now()}-2`,
                  title: 'Report Content',
                  type: 'content-section',
                  content: {} as Record<string, string>
                }
              ];
              
              // Add fields to the content section
              const contentSection = templateSections[1];
              selectedTemplateData.fields.forEach((field: any) => { // Use selectedTemplateData
                // Add each field as a property in the content object
                (contentSection.content as Record<string, string>)[field.id] = field.content || '';
              });

                setSections(templateSections);
                if (templateSections.length > 0) {
                  setActiveSectionId(templateSections[0].id);
                  setActiveSectionTitle(templateSections[0].title);
                }
              }
            }
          }
        } catch (error) {
          console.error('Error fetching templates:', error);

            // Create default sections if no templates are available
            if (!reportId) {
              const defaultSections = [
                {
                  id: `section-${Date.now()}-1`,
                  title: 'Title Page',
                  type: 'title-page',
                  content: {
                    title: "New Report",
                    subtitle: "Created with CS-Hero",
                    footer: "Confidential"
                  }
                },
                {
                  id: `section-${Date.now()}-2`,
                  title: 'Executive Summary',
                  type: 'content-section',
                  content: {
                    summary: 'This section provides a high-level overview of the report findings.'
                  }
                },
                {
                  id: `section-${Date.now()}-3`,
                  title: 'Key Information',
                  type: 'data-section',
                  content: {
                    heading: "Key Information",
                    subheading: "Important details about the report",
                    data: {
                      item1: "Value 1",
                      item2: "Value 2",
                      item3: "Value 3"
                    }
                  }
                }
              ];

            setSections(defaultSections);
            setActiveSectionId(defaultSections[0].id);
            setActiveSectionTitle(defaultSections[0].title);
          }
        }

        // Fetch clients
        try {
          const clientsResponse = await fetch('/api/clients');
          if (clientsResponse.ok) {
            const clientsData = await clientsResponse.json();
            // Handle both direct array and paginated response
            const clientsArray = Array.isArray(clientsData) ? clientsData : clientsData.items || [];
            setClients(clientsArray);
          } else {
            console.error('Failed to fetch clients:', clientsResponse.statusText);
            setClients([]);
          }
        } catch (error) {
          console.error('Error fetching clients:', error);
          setClients([]);
        }

        // Fetch report data if editing an existing report
        if (reportId) {
          try {
            const reportResponse = await fetch(`/api/v1/reports/${reportId}`);
            if (reportResponse.ok) {
              const reportData = await reportResponse.json();

              // Set report info - map from the actual API response structure
              setReportInfo({
                title: reportData.name || "Untitled Report",
                description: reportData.category || "",
                client: reportData.clientId ? {
                  'client-id': reportData.clientId,
                  'company-name': reportData.clientName || ""
                } : undefined,
                startDate: new Date().toISOString().split('T')[0],
                endDate: new Date().toISOString().split('T')[0]
              });

              // Parse content from the report data if available
              if (reportData.content) {
                // Set sections from the report content
                if (reportData.content.sections && reportData.content.sections.length > 0) {
                  setSections(reportData.content.sections);
                  setActiveSectionId(reportData.content.sections[0].id);
                  setActiveSectionTitle(reportData.content.sections[0].title);
                }
                
                // Set template from the report content
                if (reportData.content.template) {
                  setSelectedTemplate(reportData.content.template);
                  
                  // Find and set template name
                  const template = templates.find(t => t.id === reportData.content.template);
                  if (template) {
                    setTemplateName(template.name);
                  }
                }
              } else {
                // Fallback to default section if no content is available
                const defaultSection = {
                  id: `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                  title: "Report Content",
                  type: "content-section",
                  content: {
                    content: `This is the content for ${reportData.name || "the report"}.`
                  }
                };

                setSections([defaultSection]);
                setActiveSectionId(defaultSection.id);
                setActiveSectionTitle(defaultSection.title);
              }
              
              // Set style from the report data if available
              if (reportData.style) {
                setStyles(reportData.style);
              }
            } else {
              console.error('Failed to fetch report:', reportResponse.statusText);
              setError('Failed to load report. Please try again later.');
            }
          } catch (error) {
            console.error('Error fetching report:', error);
            setError('Failed to load report. Please try again later.');
          }
        }

        setLoading(false);
      } catch (err) {
        console.error('Error in data initialization:', err);
        setError('Failed to initialize editor. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, [reportId, templateId, templates]);

  // Handle menu item click
  const handleMenuClick = (item: string) => {
    if (activeItem === item) {
      setActiveItem(null);
    } else {
      setActiveItem(item);
      setActiveSectionId(null); // Reset section panel when changing menu items
    }
  };

  // Handle section click
  const handleSectionClick = (id: string, title: string) => {
    setActiveSectionId(id);
    setActiveSectionTitle(title);
    // Trigger pulse animation
    setPulsingSectionId(id);
    // Reset pulse animation after it completes
    setTimeout(() => {
      setPulsingSectionId(null);
    }, 1000);
    // Keep the panel open, don't hide it
    // setActiveItem(null); // Hide the main panel
  };

  // Handle back to sections
  const handleBackToSections = () => {
    setActiveSectionId(null);
    setActiveItem('Sections'); // Show the sections panel again
  };

  // Handle return to reports
  const handleReturnToReports = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.push('/client/reports');
    }
  };

  // Handle render report
  const handleRenderReport = async () => {
    try {
      // First save the report data to Cosmos DB
      const reportData = {
        reportName: reportInfo.title,
        description: reportInfo.description,
        clientId: reportInfo.client?.['client-id'],
        clientName: reportInfo.client?.['company-name'],
        startDate: reportInfo.startDate,
        endDate: reportInfo.endDate,
        content: {
          sections: sections,
          template: selectedTemplate
        },
        style: styles
      };

      console.log('Rendering report with data:', reportData);
      
      // Call the render API endpoint
      const response = await fetch(`/api/v1/reports/${reportId || 'new'}/render`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reportData)
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Report rendered successfully:', result);
        
        // Show the rendered HTML in a new window or modal
        if (result.html) {
          const newWindow = window.open('', '_blank');
          if (newWindow) {
            newWindow.document.write(result.html);
            newWindow.document.close();
          }
        }
        
        toast({
          title: 'Success',
          description: 'Report rendered successfully!',
        });
      } else {
        console.error('Failed to render report:', response.statusText);
        toast({
          title: 'Error',
          description: 'Failed to render report. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error rendering report:', error);
      toast({
        title: 'Error',
        description: 'Error rendering report. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle save report
  const handleSaveReport = async () => {
    try {
      // Convert section IDs to proper format for backend and simplify section structure
      const processedSections = sections.map(section => {
        // Extract the numeric part from the section ID or generate a new GUID-like string
        let sectionId = section.id;
        if (sectionId.startsWith('section-')) {
          // Generate a GUID-like string that the backend can parse
          sectionId = crypto.randomUUID();
        }
        
        // Simplify section structure to match database schema
        // Only include fields that exist in the database
        return {
          id: sectionId,
          title: section.title,
          type: section.type,
          // Serialize content as JSON string if it's an object
          content: typeof section.content === 'object' ?
            JSON.stringify(section.content) :
            section.content
        };
      });
      
      // Include content with sections and fields
      const convertedData = {
        name: reportInfo.title || "Untitled Report",
        category: reportInfo.description || "General",
        status: "Draft",
        content: {
          sections: processedSections,
          template: selectedTemplate
        },
        style: styles
      };
      
      console.log('Saving report with simplified data structure:', convertedData);
      
      console.log('Saving report with data:', convertedData);

      if (onSave) {
        onSave(convertedData);
        return;
      }

      // If it's a new report, create it
      if (!reportId) {
        const response = await fetch('/api/v1/reports', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(convertedData)
        });

        if (response.ok) {
          console.log('Report saved successfully');
          toast({
            title: 'Success',
            description: 'Report saved successfully!',
          });
          // Don't redirect - keep user in editor
        } else {
          const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
          console.error('Failed to save report:', response.statusText, errorData);
          toast({
            title: 'Error',
            description: `Failed to save report: ${errorData.message || response.statusText}`,
            variant: 'destructive',
          });
        }
      } else {
        // Update existing report
        const response = await fetch(`/api/v1/reports/${reportId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(convertedData)
        });

        if (response.ok) {
          console.log('Report updated successfully');
          toast({
            title: 'Success',
            description: 'Report updated successfully!',
          });
          // Don't redirect - keep user in editor
        } else {
          const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
          console.error('Failed to update report:', response.statusText, errorData);
          toast({
            title: 'Error',
            description: `Failed to update report: ${errorData.message || response.statusText}`,
            variant: 'destructive',
          });
        }
      }
    } catch (error) {
      console.error('Error saving report:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while saving the report. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle update report info
  const handleUpdateReportInfo = (field: string, value: any) => {
    setReportInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle update section
  const handleUpdateSection = (updatedSection: Section) => {
    setSections(sections.map(section => 
      section.id === updatedSection.id ? updatedSection : section
    ));
  };

  // Handle add section
  const handleAddSection = () => {
    const newSection: Section = {
      id: `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: `New Section ${sections.length + 1}`,
      type: 'content-section',
      content: {
        content: 'Enter your content here...'
      }
    };

    setSections(prevSections => {
      const updatedSections = [...prevSections, newSection];
      setActiveSectionId(newSection.id);
      setActiveSectionTitle(newSection.title);
      return updatedSections;
    });
  };

  // Handle template select
  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);

    // Find the selected template
    const selectedTemplate = templates.find(t => t.id === templateId);
    if (selectedTemplate) {
      setTemplateName(selectedTemplate.name);

      // Ask for confirmation before replacing sections
      if (sections.length > 0) {
        // Show confirmation dialog
        const confirmChange = window.confirm(
          'Changing the template will replace your current sections. Do you want to continue?'
        );
        
        if (confirmChange) {
          // Replace sections with template sections
          if (selectedTemplate.sections && selectedTemplate.sections.length > 0) {
            const templateSections = selectedTemplate.sections.map((section: any) => {
              const sectionTitle = section.title || "Untitled Section";
              return {
                id: `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                title: sectionTitle,
                type: section.type || "title-page",
                content: section.content || {}
              };
            });

            setSections(templateSections);
            if (templateSections.length > 0) {
              setActiveSectionId(templateSections[0].id);
              setActiveSectionTitle(templateSections[0].title);
            }
          }
        }
      } else {
        // If no sections, just add the template sections
        if (selectedTemplate.sections && selectedTemplate.sections.length > 0) {
          const templateSections = selectedTemplate.sections.map((section: any) => {
            const sectionTitle = section.title || "Untitled Section";
            return {
              id: `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              title: sectionTitle,
              type: section.type || "title-page",
              content: section.content || {}
            };
          });

          setSections(templateSections);
          if (templateSections.length > 0) {
            setActiveSectionId(templateSections[0].id);
            setActiveSectionTitle(templateSections[0].title);
          }
        }
      }
    }
  };

  // Handle update style
  const handleUpdateStyle = (field: string, value: any) => {
    setStyles(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Get active section
  const getActiveSection = () => {
    if (!activeSectionId) return null;
    return sections.find(section => section.id === activeSectionId) || null;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <div className="p-4 bg-red-50 text-red-500 rounded-md mb-4">
          <p>{error}</p>
        </div>
        <button type="button" onClick={handleReturnToReports} className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar */}
      <Sidebar
        activeItem={activeItem}
        onMenuClick={handleMenuClick}
        onSectionClick={handleSectionClick}
        onReturnToReports={handleReturnToReports}
        onSaveReport={handleSaveReport}
        onRenderReport={handleRenderReport}
        reportTitle={reportInfo.title}
      />

      {/* Main Layout Area - Two-Column Layout */}
      <div className="flex-1 ml-64 flex">
        {/* Panel Content - Integrated as a column instead of absolute position */}
        {activeItem && (
          <div className="w-96 h-[calc(100vh-4rem)] flex-shrink-0 overflow-hidden border-r">
            <PanelContent
              title={activeItem}
              onClose={() => setActiveItem(null)}
              onSectionClick={handleSectionClick}
              reportInfo={reportInfo}
              clients={clients}
              onUpdateReportInfo={handleUpdateReportInfo}
              templateName={templateName}
              sections={sections}
              onAddSection={handleAddSection}
              onUpdateSection={handleUpdateSection}
              templates={templates}
              selectedTemplate={selectedTemplate}
              onTemplateSelect={handleTemplateSelect}
              styles={styles}
              onUpdateStyle={handleUpdateStyle}
            />
          </div>
        )}

        {/* Preview Area */}
        <div className="flex-1 overflow-auto">
          <PreviewArea
            section={getActiveSection()}
            onUpdateSection={handleUpdateSection}
            allSections={sections}
            styleTemplateId={styles.styleTemplateId}
            pulsingSectionId={pulsingSectionId}
            activeSectionId={activeSectionId}
            onSectionActivate={(id: string) => {
              setActiveSectionId(id);
              const section = sections.find(s => s.id === id);
              if (section) setActiveSectionTitle(section.title);
            }}
          />
        </div>
      </div>
    </div>
  );
}
