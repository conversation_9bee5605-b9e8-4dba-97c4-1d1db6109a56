using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore; // Required for FirstOrDefaultAsync and AnyAsync
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace FY.WB.CSHero2.Application.Reports.Commands
{
    public class CreateReportCommandHandler : IRequestHandler<CreateReportCommand, Guid>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<CreateReportCommandHandler> _logger;

        public CreateReportCommandHandler(
            IApplicationDbContext context,
            ICurrentUserService currentUserService,
            ILogger<CreateReportCommandHandler> logger)
        {
            _context = context;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        public async Task<Guid> Handle(CreateReportCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Creating report with data: {RequestData}", JsonSerializer.Serialize(request.Dto));

            if (!_currentUserService.TenantId.HasValue)
            {
                throw new InvalidOperationException("TenantId is required to create a Report.");
            }

            // Handle frontend data mapping
            var reportName = !string.IsNullOrEmpty(request.Dto.ReportName) ? request.Dto.ReportName : request.Dto.Name;
            Guid? clientId = request.Dto.ClientId;
            var status = !string.IsNullOrEmpty(request.Dto.Status) ? request.Dto.Status : "Draft";
            var author = !string.IsNullOrEmpty(request.Dto.Author) ? request.Dto.Author : "System";

            // Validate ClientId if provided
            if (clientId.HasValue && clientId.Value != Guid.Empty)
            {
                if (!await _context.Clients.AnyAsync(c => c.Id == clientId.Value && c.TenantId == _currentUserService.TenantId, cancellationToken))
                {
                    _logger.LogWarning("Client with ID {ClientId} not found for tenant {TenantId}", clientId, _currentUserService.TenantId);
                    // Don't throw error, just log warning and continue without client
                    clientId = null;
                }
            }
            else
            {
                // If ClientId is empty or null, set to null
                clientId = null;
            }

            var entity = new Report
            {
                ReportNumber = !string.IsNullOrEmpty(request.Dto.ReportNumber) ? request.Dto.ReportNumber : $"RPT-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString()[..8]}",
                ClientId = clientId,
                ClientName = request.Dto.ClientName ?? string.Empty,
                Name = reportName,
                Category = request.Dto.Category ?? "General",
                SlideCount = request.Dto.SlideCount,
                Status = status,
                Author = author,
                TenantId = _currentUserService.TenantId.Value
            };

            _context.Reports.Add(entity);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created report {ReportId} with name '{ReportName}'", entity.Id, entity.Name);

            // Create sections and fields if provided
            if (request.Dto.Content?.Sections?.Count > 0)
            {
                await CreateReportSections(entity.Id, request.Dto.Content.Sections, cancellationToken);
            }

            return entity.Id;
        }

        private async Task CreateReportSections(Guid reportId, List<FY.WB.CSHero2.Application.Reports.Dtos.ReportSectionDto> sectionDtos, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Creating {SectionCount} sections for report {ReportId}", sectionDtos.Count, reportId);

            for (int i = 0; i < sectionDtos.Count; i++)
            {
                var sectionDto = sectionDtos[i];
                var sectionTitle = !string.IsNullOrEmpty(sectionDto.Title) ? sectionDto.Title : sectionDto.SectionTitle;

                // Generate a new GUID for the section before adding it to the context
                var sectionId = Guid.NewGuid();
                var sectionType = sectionDto.Type ?? "text";
                var sectionOrder = sectionDto.Order > 0 ? sectionDto.Order : i;

                var section = new ReportSection(
                    sectionId,
                    reportId,
                    sectionTitle ?? $"Section {i + 1}",
                    sectionType,
                    sectionOrder)
                {
                    Content = sectionDto.Content != null ? JsonSerializer.Serialize(sectionDto.Content) : string.Empty,
                    TenantId = _currentUserService.TenantId.Value
                };

                _context.ReportSections.Add(section);

                // Create fields for this section using the pre-assigned section ID
                if (sectionDto.Fields?.Count > 0)
                {
                    await CreateSectionFields(sectionId, sectionDto.Fields, cancellationToken);
                }
            }

            // Save all sections and fields together
            await _context.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Successfully created sections and fields for report {ReportId}", reportId);
        }

        private async Task CreateSectionFields(Guid sectionId, List<FY.WB.CSHero2.Application.Reports.Dtos.ReportSectionFieldDto> fieldDtos, CancellationToken cancellationToken)
        {
            if (fieldDtos == null || fieldDtos.Count == 0) return;

            // Validate section exists before adding fields
            var sectionExists = await _context.ReportSections
                .AnyAsync(s => s.Id == sectionId, cancellationToken);

            if (!sectionExists)
            {
                _logger.LogError("Cannot create fields for non-existent section {SectionId}", sectionId);
                throw new InvalidOperationException($"ReportSection {sectionId} not found");
            }

            for (int i = 0; i < fieldDtos.Count; i++)
            {
                var fieldDto = fieldDtos[i];

                var fieldId = Guid.NewGuid();
                var field = new ReportSectionField(
                    fieldId,
                    sectionId,
                    fieldDto.Name ?? $"field_{i}",
                    fieldDto.Type ?? "text",
                    fieldDto.Content != null ? JsonSerializer.Serialize(fieldDto.Content) : string.Empty,
                    fieldDto.Order > 0 ? fieldDto.Order : i
                )
                {
                    Label = fieldDto.Label ?? fieldDto.Name ?? $"Field {i + 1}",
                    Value = fieldDto.Value != null ? JsonSerializer.Serialize(fieldDto.Value) : string.Empty,
                    IsRequired = fieldDto.IsRequired,
                    Metadata = fieldDto.Metadata != null ? JsonSerializer.Serialize(fieldDto.Metadata) : string.Empty,
                    TenantId = _currentUserService.TenantId.Value
                };

                _context.ReportSectionFields.Add(field);

                _logger.LogDebug("Added field {FieldName} to section {SectionId}", field.Name, sectionId);
            }

            _logger.LogInformation("Prepared {FieldCount} fields for section {SectionId}", fieldDtos.Count, sectionId);
        }
    }
}
